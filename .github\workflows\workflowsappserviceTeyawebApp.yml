name: Build and Deploy ASP.NET App to Azure Web App

# ✅ Add permissions here
permissions:
  id-token: write
  contents: read

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      # ✅ Step 1: Checkout source code
      - name: Checkout code
        uses: actions/checkout@v4

      # ✅ Step 2: Setup .NET SDK
      - name: Setup .NET SDK
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.x'

      # ✅ Step 3: Restore dependencies
      - name: Restore dependencies
        run: dotnet restore Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/TeyaWebApp.csproj

      # ✅ Step 4: Build the app
      - name: Build project
        run: dotnet build Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/TeyaWebApp.csproj --configuration Release --no-restore

      # ✅ Step 5: Publish the app
      - name: Publish project
        run: dotnet publish Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/TeyaWebApp.csproj --configuration Release --output ./publish --no-build

      # ✅ Step 6: Login to Azure using Service Principal
      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: d20b72c2-619c-4b74-bb31-2194e8e5a137
          tenant-id: 03a052f6-4a19-4ae7-8ed7-47b794e0e597
          subscription-id: d03f8f15-ed87-4701-8864-0dcfefcc5671
          client-secret: ****************************************

      # ✅ Step 7: Deploy to Azure Web App
      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v3
        with:
          app-name: Teya-Webapp-Dev
          package: ./publish
